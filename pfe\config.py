#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
PFE (Pupil Foreshortening Error) 配置文件
基于论文: <PERSON> & Petrov (2016) Behav Res
"""

import numpy as np

# =============================================================================
# 网格标定参数
# =============================================================================

# 网格配置
GRID_ROWS = 6  # 垂直方向点数
GRID_COLS = 8  # 水平方向点数
TOTAL_GRID_POINTS = GRID_ROWS * GRID_COLS  # 总点数: 48

# 显示参数
FIXATION_DURATION = 2.0  # 每个点的注视时间（秒）
ANALYSIS_WINDOW = 1.0    # 分析窗口（后3秒）
BASELINE_DURATION = 1.0  # 基线时间（前1秒不分析）

# 标定重复参数
CALIBRATION_REPETITIONS = 2  # 每个点显示的总次数
RANDOMIZE_ORDER = True       # 是否随机显示顺序

# 练习阶段参数
PRACTICE_POINTS = 5         # 正式标定前的练习点数
ENABLE_PRACTICE = True       # 是否启用练习阶段

# 刺激参数
DOT_SIZE = 20           # 圆点大小（像素）
DOT_COLOR = 'Black'     # 圆点颜色
BACKGROUND_COLOR = 'gray'  # 背景颜色
FIXATION_CROSS_SIZE = 30   # 注视十字大小

# 屏幕参数（将从EyeLink设置中获取）
DEFAULT_SCREEN_WIDTH = 1920
DEFAULT_SCREEN_HEIGHT = 1080
CALIBRATION_AREA_RATIO = 0.5  # 校准区域比例（与EyeLink设置一致）

# =============================================================================
# 几何模型参数
# =============================================================================

# 相机和屏幕坐标（用户提供的粗略测量值）
# 注意：这些值可能需要通过优化算法进行调整
#gyd
# CAMERA_POSITION = {
#     'x': 40,   # 相对相机的x坐标 (mm)
#     'y': -85,  # 相对相机的y坐标 (mm) 
#     'z': 560   # 相对相机的z坐标 (mm)
# }

# SCREEN_POSITION = {
#     'x': -200,  # 相对屏幕左上角的x坐标 (mm)
#     'y': 155,   # 相对屏幕左上角的y坐标 (mm)
#     'z': 710    # 相对屏幕左上角的z坐标 (mm)
# }

#mzx
CAMERA_POSITION = {
    'x': -10,   # 相机相对人眼的x坐标 (mm)
    'y': -130,  # 相机相对人眼的y坐标 (mm) 
    'z': 590,   # 相机相对人眼的z坐标 (mm)
}

SCREEN_POSITION = {
    'x': -195,  # 屏幕左上角相对人眼的x坐标 (mm)
    'y': 195,   # 屏幕左上角相对人眼的y坐标 (mm)
    'z': 690,    # 屏幕左上角相对人眼的z坐标 (mm)
}
EYE_POSITION = {'x': 0, 'y': 0, 'z': 0}

def transform(pos, origin):
    # 平移
    tx = pos['x'] - origin['x']
    ty = -(pos['y'] - origin['y'])  # Y 轴反向
    tz = -(pos['z'] - origin['z'])  # Z 轴反向
    return {'x': tx, 'y': ty, 'z': tz}




# =============================================================================
# 数据处理参数
# =============================================================================

# 瞳孔数据过滤
MIN_PUPIL_SIZE = 100.0    # 最小有效瞳孔面积
MAX_PUPIL_SIZE = 6000.0   # 最大有效瞳孔面积
SAMPLING_RATE = 1000      # EyeLink采样率 (Hz)
# screen_width_mm: float = 531.36,
# screen_height_mm: float = 298.89,

# 眨眼检测和过滤
BLINK_THRESHOLD = 0.1     # 眨眼检测阈值
VELOCITY_THRESHOLD = 500.0  # 速度阈值（像素/毫秒）

# 数据质量控制
MIN_VALID_POINTS = 1     # 最少有效标定点数（总点数的一半）

# 眼睛选择参数
EYE_SELECTION = 'left'   # 用于分析的眼睛选择: 'left', 'right', 'both'（双眼平均）


# =============================================================================
# 文件路径和命名
# =============================================================================

# 数据文件命名
CALIBRATION_DATA_SUFFIX = '_pfe_calibration'
CORRECTION_DATA_SUFFIX = '_pfe_corrected'

# 结果保存
SAVE_CALIBRATION_PLOT = True
SAVE_CORRECTION_PLOT = True
PLOT_DPI = 300

# 可视化参数
SHOW_CALIBRATION_RESULT = True  # 是否显示校准结果图
PUPIL_SIZE_SCALE_FACTOR = 0.01  # 瞳孔大小缩放因子（圆点半径 = 瞳孔大小 * 缩放因子）
MIN_DOT_RADIUS = 5              # 最小圆点半径
MAX_DOT_RADIUS = 50             # 最大圆点半径
SHOW_PUPIL_RANGE = True         # 是否在点上显示瞳孔大小范围（最大值-最小值）

# 数据分析参数
AUTO_ANALYZE_AFTER_CALIBRATION = True  # 标定完成后是否自动分析数据

# =============================================================================
# 实验流程参数
# =============================================================================

# 指导语
INSTRUCTION_TEXT = """
PFE瞳孔前缩误差标定

本程序将显示48个网格点，请按照以下步骤操作：

1. 保持头部稳定，眼睛注视屏幕中央
2. 当圆点出现时，立即将视线移动到圆点上
3. 保持注视圆点直到它消失（4秒）
4. 然后等待下一个圆点出现

整个过程大约需要3-4分钟。
请尽量保持专注，避免眨眼过多。

按空格键开始标定...
"""

# 休息提示
BREAK_INTERVAL = 50  # 每16个点休息一次
BREAK_TEXT = """
已完成 {completed}/{total} 个点

请稍作休息，保持头部稳定。
准备好后按空格键继续...
"""

# 完成提示
COMPLETION_TEXT = """
标定完成！

数据已保存，正在分析...
请稍等片刻。
"""

# =============================================================================
# 调试和日志参数
# =============================================================================

# 日志级别
LOG_LEVEL = 'INFO'  # 'DEBUG', 'INFO', 'WARNING', 'ERROR'

# 调试模式
DEBUG_MODE = False
DUMMY_MODE = False  # 是否使用虚拟模式（测试用）

# 可视化参数
SHOW_GAZE_POSITION = True   # 是否显示注视位置
SHOW_PUPIL_SIZE = True      # 是否显示瞳孔大小
REAL_TIME_PLOT = False      # 是否实时绘图（影响性能）

# =============================================================================
# 矫正算法参数
# =============================================================================

# 几何模型矫正
USE_GEOMETRIC_MODEL = True
OPTIMIZE_PARAMETERS = True  # 是否优化几何参数



# =============================================================================
# 工具函数
# =============================================================================

def get_grid_positions(screen_width, screen_height, calibration_ratio=CALIBRATION_AREA_RATIO):
    """
    计算网格点的屏幕坐标
    
    Args:
        screen_width: 屏幕宽度
        screen_height: 屏幕高度  
        calibration_ratio: 校准区域比例
        
    Returns:
        list: [(x, y), ...] 网格点坐标列表
    """
    # 计算有效区域
    effective_width = screen_width * calibration_ratio
    effective_height = screen_height * calibration_ratio
    
    # 计算偏移量（居中）
    offset_x = (screen_width - effective_width) / 2
    offset_y = (screen_height - effective_height) / 2
    
    # 计算网格间距
    step_x = effective_width / (GRID_COLS - 1)
    step_y = effective_height / (GRID_ROWS - 1)
    
    # 生成网格点
    positions = []
    for row in range(GRID_ROWS):
        for col in range(GRID_COLS):
            x = offset_x + col * step_x
            y = offset_y + row * step_y
            positions.append((x, y))
    
    return positions

def validate_config():
    """验证配置参数的有效性"""
    assert GRID_ROWS > 0 and GRID_COLS > 0, "网格尺寸必须大于0"
    assert FIXATION_DURATION > ANALYSIS_WINDOW, "注视时间必须大于分析窗口"
    assert ANALYSIS_WINDOW > 0, "分析窗口必须大于0"
    assert MIN_PUPIL_SIZE < MAX_PUPIL_SIZE, "瞳孔大小范围无效"
    assert 0 < CALIBRATION_AREA_RATIO <= 1, "校准区域比例必须在(0,1]范围内"
    assert EYE_SELECTION in ['left', 'right', 'both'], "眼睛选择参数必须是 'left', 'right' 或 'both'"
    
    print("✓ 配置参数验证通过")

if __name__ == "__main__":
    # 测试配置
    # validate_config()
    

    eye_new = transform(EYE_POSITION, SCREEN_POSITION)
    camera_new = transform(CAMERA_POSITION, SCREEN_POSITION)

    print("Camera in new coords:", camera_new)
    print("left Eye in new coords:", eye_new)
    print("right Eye in new coords:", eye_new['x']+60, eye_new['y'], eye_new['z'])
    
    # # 显示主要参数
    # print(f"网格配置: {GRID_COLS}×{GRID_ROWS} = {TOTAL_GRID_POINTS}个点")
    # print(f"每点时间: {FIXATION_DURATION}秒 (分析后{ANALYSIS_WINDOW}秒)")
    # print(f"预计总时间: {TOTAL_GRID_POINTS * FIXATION_DURATION / 60:.1f}分钟")
    
    # # 测试网格位置计算
    # positions = get_grid_positions(1920, 1080)
    # print(f"网格点示例: {positions[:3]}...")
